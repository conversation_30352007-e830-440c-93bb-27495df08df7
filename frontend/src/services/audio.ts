import {
  convertBlobToBase64,
  ensureSingleValidAudioTrack,
  getAudioStream,
  getBrowserSupportedMimeType,
  MimeType,
  EVIWebAudioPlayer
} from 'hume'

export class AudioService {
  private recorder: MediaRecorder | null = null
  private audioStream: MediaStream | null = null
  private player: EVIWebAudioPlayer | null = null
  private isRecording = false
  private isPlaying = false
  private onAudioDataCallback: ((audioData: string) => void) | null = null

  constructor() {
    this.player = new EVIWebAudioPlayer()
  }

  /**
   * Initialize audio service - must be called in a user gesture
   */
  async init(): Promise<void> {
    if (this.player) {
      await this.player.init()
    }
  }

  /**
   * Start audio recording and streaming
   */
  async startRecording(onAudioData: (audioData: string) => void): Promise<void> {
    if (this.isRecording) {
      console.warn('Already recording')
      return
    }

    try {
      // Get microphone stream
      this.audioStream = await getAudioStream()
      ensureSingleValidAudioTrack(this.audioStream)

      // Get supported MIME type
      const mimeTypeResult = getBrowserSupportedMimeType()
      const mimeType = mimeTypeResult.success ? mimeTypeResult.mimeType : MimeType.WEBM

      // Create MediaRecorder
      this.recorder = new MediaRecorder(this.audioStream, { mimeType })
      this.onAudioDataCallback = onAudioData

      // Set up data handler
      this.recorder.ondataavailable = async (event: BlobEvent) => {
        if (event.data.size > 0 && this.onAudioDataCallback) {
          try {
            const base64Data = await convertBlobToBase64(event.data)
            this.onAudioDataCallback(base64Data)
          } catch (error) {
            console.error('Error converting audio to base64:', error)
          }
        }
      }

      this.recorder.onerror = (event) => {
        console.error('MediaRecorder error:', event)
      }

      // Start recording with 100ms time slices (recommended by Hume)
      this.recorder.start(100)
      this.isRecording = true

      console.log('🎤 Audio recording started')
    } catch (error) {
      console.error('Failed to start audio recording:', error)
      throw error
    }
  }

  /**
   * Stop audio recording
   */
  stopRecording(): void {
    if (!this.isRecording) {
      return
    }

    if (this.recorder && this.recorder.state !== 'inactive') {
      this.recorder.stop()
    }

    if (this.audioStream) {
      this.audioStream.getTracks().forEach(track => track.stop())
      this.audioStream = null
    }

    this.recorder = null
    this.onAudioDataCallback = null
    this.isRecording = false

    console.log('🎤 Audio recording stopped')
  }

  /**
   * Play audio output from Hume
   */
  async playAudio(audioMessage: any): Promise<void> {
    if (!this.player) {
      console.error('Audio player not initialized')
      return
    }

    try {
      await this.player.enqueue(audioMessage)
      this.isPlaying = true
      
      // Listen for play/stop events
      this.player.addEventListener('play', () => {
        this.isPlaying = true
      })
      
      this.player.addEventListener('stop', () => {
        this.isPlaying = false
      })

    } catch (error) {
      console.error('Error playing audio:', error)
    }
  }

  /**
   * Stop audio playback
   */
  stopPlayback(): void {
    if (this.player) {
      this.player.stop()
      this.isPlaying = false
    }
  }

  /**
   * Set playback volume (0.0 to 1.0)
   */
  setVolume(volume: number): void {
    if (this.player) {
      this.player.setVolume(volume)
    }
  }

  /**
   * Mute audio playback
   */
  mute(): void {
    if (this.player) {
      this.player.mute()
    }
  }

  /**
   * Unmute audio playback
   */
  unmute(): void {
    if (this.player) {
      this.player.unmute()
    }
  }

  /**
   * Get current recording state
   */
  getIsRecording(): boolean {
    return this.isRecording
  }

  /**
   * Get current playback state
   */
  getIsPlaying(): boolean {
    return this.isPlaying
  }

  /**
   * Cleanup resources
   */
  dispose(): void {
    this.stopRecording()
    this.stopPlayback()
    
    if (this.player) {
      this.player.dispose()
      this.player = null
    }
  }
}

// Create singleton instance
export const audioService = new AudioService()
